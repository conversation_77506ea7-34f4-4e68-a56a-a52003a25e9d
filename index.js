const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
puppeteer.use(StealthPlugin({
  enabledEvasions: new Set([
    "chrome.app",
    "chrome.csi",
    "defaultArgs",
    "navigator.plugins"
  ])
}));

const run = async () => {
  const cnpj = process.argv[2];

  if (!cnpj) {
    console.log('Uso: yarn node index.js <seu-cnpj>');
    process.exit(1);
  }

  let browser;
  try {
    console.log('Iniciando o navegador...');
    browser = await puppeteer.launch({
      args: [
        '--disable-blink-features=AutomationControlled',
        '--window-size=1920,1080'
      ],
      headless: false
    });
    const page = await browser.newPage();

    console.log('Acessando a página de identificação...');
    await page.goto('https://www8.receita.fazenda.gov.br/SimplesNacional/Aplicacoes/ATSPO/pgmei.app/Identificacao', { waitUntil: 'networkidle2' });

    const cleanedCnpj = cnpj.replace(/[.\/-]/g, '');
    console.log(`Preenchendo o CNPJ: ${cnpj} (limpo: ${cleanedCnpj})`);
    await page.type('#cnpj', cleanedCnpj);
    await page.click('button[type="submit"]');

    console.log('Aguardando a página carregar...');
    await page.waitForNavigation({ waitUntil: 'networkidle2' });

    await page.screenshot({ path: 'screenshots/1.png' });
  } finally {
    if (browser) {
      await browser.close();
      console.log('Navegador fechado.');
    }
  }
};

run();
