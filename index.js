const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
puppeteer.use(StealthPlugin({
  enabledEvasions: new Set([
    "chrome.app",
    "chrome.csi",
    "defaultArgs",
    "navigator.plugins"
  ])
}));

const run = async () => {
  const cnpj = process.argv[2];

  if (!cnpj) {
    console.log('Uso: yarn node index.js <seu-cnpj>');
    process.exit(1);
  }

  let browser;
  try {
    console.log('Iniciando o navegador...');
    browser = await puppeteer.launch({
      args: [
        '--disable-blink-features=AutomationControlled',
        '--window-size=1920,1080'
      ],
      headless: false
    });
    const page = await browser.newPage();

    console.log('Acessando a página de identificação...');
    await page.goto('https://www8.receita.fazenda.gov.br/SimplesNacional/Aplicacoes/ATSPO/pgmei.app/Identificacao', { waitUntil: 'networkidle2' });

    const cleanedCnpj = cnpj.replace(/[.\/-]/g, '');
    console.log(`Preenchendo o CNPJ: ${cnpj} (limpo: ${cleanedCnpj})`);
    await page.type('#cnpj', cleanedCnpj);
    await page.click('button[type="submit"]');

    console.log('Aguardando a página carregar...');
    await page.waitForNavigation({ waitUntil: 'networkidle2' });
    await page.screenshot({ path: 'screenshots/1.png' });

    // Clica no link "Emitir Guia de Pagamento (DAS)"
    await page.click('a[href="/SimplesNacional/Aplicacoes/ATSPO/pgmei.app/emissao"]')
    await page.waitForNavigation({ waitUntil: 'networkidle2' });
    await page.screenshot({ path: 'screenshots/2.png' });

    // Clica no botão do dropdown para abrir
    await page.click('button[data-id="anoCalendarioSelect"]');

    // Aguarda o dropdown abrir
    await page.waitForSelector('.dropdown-menu.open', { visible: true });

    // Clica na opção 2025 usando XPath
    await page.waitForXPath('//li//span[@class="text" and contains(text(), "2025")]', { visible: true });
    const option2025 = await page.$x('//li//span[@class="text" and contains(text(), "2025")]');
    if (option2025.length > 0) {
      await option2025[0].click();
      console.log('Ano 2025 selecionado com sucesso!');
    } else {
      console.log('Opção 2025 não encontrada!');
    }

    await page.screenshot({ path: 'screenshots/3.png' });


  } finally {
    if (browser)
      await browser.close();
  }
};

run();
